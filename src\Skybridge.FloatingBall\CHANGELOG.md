# Changelog - 悬浮球应用程序

## Version 1.2.0 - Windows 7 兼容性更新 (Windows 7 Compatibility Update)

### 🖼️ Windows 7 图标兼容性 (Windows 7 Icon Compatibility)
- **替换 Emoji 图标**: 将所有 emoji 图标替换为 Windows 7 兼容的矢量图形
- **自定义 SVG 图标**: 使用 Canvas 和 Path 元素创建现代图标
- **图标映射**:
  - 👁 → 自定义眼睛图标 (显示功能)
  - 🙈 → 自定义隐藏图标 (隐藏功能)
  - ⚙ → 自定义齿轮图标 (设置功能)
  - ℹ → 自定义信息图标 (关于功能)
  - ❌ → 自定义 X 图标 (退出功能)
  - 🌐 → 自定义地球图标 (网址链接)
  - 🌟 → 自定义星形图标 (中心装饰)
- **保持视觉一致性**: 所有图标使用统一的主题色 #3389F1
- **向后兼容**: 确保在 Windows 7 及更高版本系统上正确显示

### 🔧 技术改进 (Technical Improvements)
- **矢量图形**: 使用 WPF Canvas 和几何图形替代 Unicode emoji
- **可缩放性**: 图标在不同 DPI 设置下保持清晰
- **性能优化**: 减少字体依赖，提高渲染性能
- **跨系统兼容**: 消除对特定字体或 emoji 支持的依赖

## Version 1.1.0 - 中文本地化版本 (Chinese Localization)

### 🌏 完整中文本地化 (Complete Chinese Localization)
- **界面翻译**: 所有用户界面元素完全翻译为中文
- **菜单本地化**: 右键菜单项目全部中文化
  - 快捷链接 (Quick Links)
  - 显示/隐藏 (Show/Hide)
  - 设置 (Settings)
  - 关于 (About)
  - 退出 (Exit)
- **设置窗口**: 设置界面完全中文化
  - 窗口标题: "悬浮球设置"
  - 表单标签: "名称"、"网址"
  - 按钮: "添加"、"更新"、"删除"、"清空"、"保存并关闭"、"取消"
- **消息对话框**: 所有错误和确认消息中文化
- **应用程序元数据**: 程序标题和描述更新为中文

### 🎨 Visual Updates (Previous)
- **New Theme Color**: Updated primary color scheme from blue to #3389F1
- **Gradient Refresh**: All gradients now use the new theme color with proper shading
  - Normal state: #3389F1 → #2470D1 → #1557B1
  - Hover state: #4A9AF5 → #3580E1 → #2667C1
- **System Tray Icon**: Updated tray icon to match the new theme color

### 📍 Positioning Improvements
- **Bottom-Right Default**: Ball now appears in bottom-right corner instead of top-right
- **Position Memory**: Application now remembers the last position where you placed the ball
- **Smart Restoration**: Position is restored on next launch with screen boundary validation
- **Automatic Saving**: Position is saved automatically when:
  - Ball is dragged to a new location
  - Application is closed
  - Window location changes

### 🔧 Technical Enhancements
- **Settings Integration**: Added user settings for WindowLeft and WindowTop
- **Boundary Checking**: Ensures ball stays visible even if screen resolution changes
- **Graceful Fallback**: Uses default bottom-right position if no saved position exists
- **Real-time Saving**: Position updates are saved immediately, not just on close

### 📁 Files Modified (Chinese Localization)
- `MainWindow.xaml`: 中文化右键菜单和窗口标题
- `MainWindow.xaml.cs`: 中文化系统托盘文本和错误消息
- `SettingsWindow.xaml`: 完整的设置界面中文化
- `SettingsWindow.xaml.cs`: 中文化所有验证消息和对话框
- `Properties/AssemblyInfo.cs`: 更新应用程序元数据为中文
- `README_CN.md`: 创建完整的中文文档

### 📁 Files Modified (Previous Features)
- `MainWindow.xaml`: Updated gradient color definitions and URL menu
- `MainWindow.xaml.cs`: Added position persistence logic, URL management, and new positioning
- `SettingsWindow.xaml`: Created modern settings window
- `SettingsWindow.xaml.cs`: Implemented URL management functionality
- `Properties/Settings.settings`: Added WindowLeft, WindowTop, and UrlList settings
- `Properties/Settings.Designer.cs`: Generated properties for new settings
- `README.md`: Updated documentation to reflect new features

### 🎯 User Experience
- **Consistent Positioning**: Ball always appears where you last left it
- **Visual Consistency**: New color scheme provides better visual appeal
- **Reliable Memory**: Position is preserved across application restarts
- **Screen Adaptation**: Handles screen resolution changes gracefully

### 🔄 Backward Compatibility
- **First Run**: New users get the default bottom-right positioning
- **Existing Users**: Will see the ball in bottom-right on first run with new version
- **Settings Migration**: Automatic handling of missing or invalid position settings

## Version 1.0.0 - Initial Release

### ✨ Core Features
- Beautiful floating ball with gradient effects
- Right-click context menu functionality
- Hide/Show capabilities
- System tray integration
- Drag and drop positioning
- Smooth animations and hover effects
- Keyboard shortcuts (Ctrl+H)
- Always-on-top behavior
- Transparent background
- No external dependencies

### 🎨 Visual Design
- Radial gradient ball design
- Pulsing glow animation
- Hover scaling effects
- Drop shadow for depth
- Professional styling

### 🔧 System Integration
- System tray with custom icon
- Context menus for both ball and tray
- No taskbar presence
- Windows Forms integration for tray functionality
- WPF for main UI components
