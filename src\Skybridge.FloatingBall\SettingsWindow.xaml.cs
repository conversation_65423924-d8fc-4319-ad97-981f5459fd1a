using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace Skybridge.FloatingBall
{
    /// <summary>
    /// Interaction logic for SettingsWindow.xaml
    /// </summary>
    public partial class SettingsWindow : Window
    {
        private ObservableCollection<UrlItem> _urlItems;
        private bool _hasChanges = false;

        public SettingsWindow()
        {
            InitializeComponent();
            InitializeUrlList();
            LoadUrls();
        }

        private void InitializeUrlList()
        {
            _urlItems = new ObservableCollection<UrlItem>();
            UrlListBox.ItemsSource = _urlItems;
        }

        private void LoadUrls()
        {
            _urlItems.Clear();

            if (Properties.Settings.Default.UrlList != null)
            {
                foreach (string urlData in Properties.Settings.Default.UrlList)
                {
                    var parts = urlData.Split('|');
                    if (parts.Length == 2)
                    {
                        _urlItems.Add(new UrlItem { Name = parts[0], Url = parts[1] });
                    }
                }
            }
        }

        private void SaveUrls()
        {
            if (Properties.Settings.Default.UrlList == null)
            {
                Properties.Settings.Default.UrlList = new StringCollection();
            }

            Properties.Settings.Default.UrlList.Clear();

            foreach (var item in _urlItems)
            {
                Properties.Settings.Default.UrlList.Add($"{item.Name}|{item.Url}");
            }

            Properties.Settings.Default.Save();
            _hasChanges = false;
        }

        private void UrlListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedItem = UrlListBox.SelectedItem as UrlItem;

            if (selectedItem != null)
            {
                UrlNameTextBox.Text = selectedItem.Name;
                UrlTextBox.Text = selectedItem.Url;
                UpdateButton.IsEnabled = true;
                DeleteButton.IsEnabled = true;
            }
            else
            {
                UpdateButton.IsEnabled = false;
                DeleteButton.IsEnabled = false;
            }
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                var newItem = new UrlItem
                {
                    Name = UrlNameTextBox.Text.Trim(),
                    Url = UrlTextBox.Text.Trim()
                };

                _urlItems.Add(newItem);
                _hasChanges = true;
                ClearForm();

                // Select the newly added item
                UrlListBox.SelectedItem = newItem;
            }
        }

        private void UpdateButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = UrlListBox.SelectedItem as UrlItem;

            if (selectedItem != null && ValidateInput())
            {
                selectedItem.Name = UrlNameTextBox.Text.Trim();
                selectedItem.Url = UrlTextBox.Text.Trim();
                _hasChanges = true;

                // Refresh the list to show updated values
                UrlListBox.Items.Refresh();
            }
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = UrlListBox.SelectedItem as UrlItem;

            if (selectedItem != null)
            {
                var result = System.Windows.MessageBox.Show(
                    $"您确定要删除 '{selectedItem.Name}' 吗？",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _urlItems.Remove(selectedItem);
                    _hasChanges = true;
                    ClearForm();
                }
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            UrlListBox.SelectedItem = null;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            SaveUrls();
            this.DialogResult = true;
            this.Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_hasChanges)
            {
                var result = System.Windows.MessageBox.Show(
                    "您有未保存的更改。确定要取消吗？",
                    "未保存的更改",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            this.DialogResult = false;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(UrlNameTextBox.Text))
            {
                System.Windows.MessageBox.Show("请输入网址名称。", "验证错误",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                UrlNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(UrlTextBox.Text))
            {
                System.Windows.MessageBox.Show("请输入网址。", "验证错误",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                UrlTextBox.Focus();
                return false;
            }

            // Basic URL validation
            if (!Uri.TryCreate(UrlTextBox.Text.Trim(), UriKind.Absolute, out Uri result) ||
                (result.Scheme != Uri.UriSchemeHttp && result.Scheme != Uri.UriSchemeHttps))
            {
                System.Windows.MessageBox.Show("请输入有效的网址（以 http:// 或 https:// 开头）。",
                    "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                UrlTextBox.Focus();
                return false;
            }

            // Check for duplicate names (excluding current selection for updates)
            var selectedItem = UrlListBox.SelectedItem as UrlItem;
            var duplicateName = _urlItems.Any(item =>
                item != selectedItem &&
                string.Equals(item.Name, UrlNameTextBox.Text.Trim(), StringComparison.OrdinalIgnoreCase));

            if (duplicateName)
            {
                System.Windows.MessageBox.Show("已存在同名的网址。请选择不同的名称。",
                    "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                UrlNameTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            UrlNameTextBox.Text = "";
            UrlTextBox.Text = "";
            UpdateButton.IsEnabled = false;
            DeleteButton.IsEnabled = false;
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (_hasChanges && this.DialogResult != true)
            {
                var result = System.Windows.MessageBox.Show(
                    "您有未保存的更改。确定要在不保存的情况下关闭吗？",
                    "未保存的更改",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }
    }

    public class UrlItem
    {
        public string Name { get; set; }
        public string Url { get; set; }
    }
}