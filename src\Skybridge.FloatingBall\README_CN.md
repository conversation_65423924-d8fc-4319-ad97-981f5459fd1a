# 悬浮球应用程序

一个美观的 WPF 悬浮球应用程序，具有右键菜单功能和系统托盘集成。

## 功能特性

### 🎨 精美设计
- **渐变球体**: 采用主题色 #3389F1 的美观渐变和径向光照效果
- **流畅动画**: 悬停效果和缩放动画
- **脉冲光晕**: 持续的微妙脉冲动画，增强视觉吸引力
- **投影效果**: 逼真的阴影效果，增加立体感

### 🖱️ 交互功能
- **可拖拽**: 点击并拖拽球体在屏幕上移动
- **悬停效果**: 鼠标悬停时球体外观变化并放大
- **右键菜单**: 带有各种选项的上下文菜单

### 📋 右键菜单选项
- **快捷链接**: 显示您保存的网址的动态部分（点击在浏览器中打开）
- **显示**: 使球体可见（当隐藏时）
- **隐藏**: 隐藏球体
- **设置**: 用于网址管理的现代设置窗口
- **关于**: 应用程序信息
- **退出**: 关闭应用程序

### 🔧 系统集成
- **系统托盘**: 最小化到系统托盘，带有自定义主题图标
- **托盘右键菜单**: 右键点击托盘图标进行快速操作
- **双击托盘**: 双击托盘图标切换可见性
- **始终置顶**: 球体保持在其他窗口之上
- **无任务栏**: 不会占用任务栏空间
- **位置记忆**: 记住重新打开时的最后位置
- **智能定位**: 初始出现在屏幕右下角

### 🔗 网址管理
- **快速访问**: 添加常用网址以便一键访问
- **设置窗口**: 用于管理网址的现代界面
- **网址验证**: 自动验证网址格式
- **持久存储**: 网址在会话之间保存和恢复
- **浏览器集成**: 在默认浏览器中打开网址

### ⌨️ 键盘快捷键
- **Ctrl+H**: 切换球体可见性（当球体窗口获得焦点时）

## 技术详情

### 系统要求
- .NET Framework 4.6.1 或更高版本
- Windows 7 及更高版本操作系统
- 无需额外的 NuGet 包
- **Windows 7 兼容**: 所有图标使用矢量图形，完全兼容 Windows 7

### 架构
- **WPF 应用程序**: 使用 Windows Presentation Foundation 构建
- **XAML 样式**: 在 XAML 中定义的美观 UI，包含渐变和动画
- **System.Windows.Forms 集成**: 用于系统托盘功能
- **事件驱动**: 响应鼠标和键盘事件

### 窗口属性
- **透明背景**: 与桌面无缝集成
- **无窗口边框**: 无边框窗口，外观简洁
- **置顶**: 始终保持在其他窗口之上
- **不可调整大小**: 固定大小以保持一致的外观

## 使用方法

### 基本操作
1. **启动**: 运行可执行文件启动悬浮球（出现在右下角）
2. **移动**: 点击并拖拽重新定位球体（位置自动保存）
3. **隐藏/显示**: 右键选择隐藏/显示，或使用 Ctrl+H
4. **系统托盘**: 隐藏时从系统托盘访问
5. **退出**: 右键选择退出，或从系统托盘退出

### 网址管理
1. **打开设置**: 右键点击球体并选择"设置"
2. **添加网址**: 输入名称和网址，然后点击"添加"
3. **编辑网址**: 从列表中选择网址，修改后点击"更新"
4. **删除网址**: 选择网址并点击"删除"
5. **访问网址**: 右键点击球体并点击任何网址名称在浏览器中打开

### 功能特性
- **位置记忆**: 球体记住重启时的最后位置
- **现代 UI**: 美观、现代的界面，带有流畅动画
- **网址验证**: 确保网址在保存前格式正确
- **持久存储**: 所有设置和网址自动保存

## 构建

使用 Visual Studio 或 MSBuild 构建：

```bash
MSBuild.exe Skybridge.FloatingBall.csproj /p:Configuration=Release
```

## 自定义

应用程序可以通过修改以下内容轻松自定义：

- **颜色**: 更改 MainWindow.xaml 资源中的渐变颜色
- **大小**: 修改球体的宽度/高度属性
- **动画**: 调整动画持续时间和效果
- **位置**: 更改 InitializeFloatingBall() 中的初始定位逻辑
- **图标**: 替换系统托盘图标创建逻辑

## 版本历史

### v1.2.0 - Windows 7 兼容性更新
- **Windows 7 完全兼容**: 替换所有 emoji 图标为矢量图形
- **自定义图标系统**: 使用 WPF Canvas 创建的现代图标
- **向后兼容性**: 确保在旧版 Windows 系统上正确显示
- **性能优化**: 减少字体依赖，提高渲染性能

### v1.1.0 - 中文本地化版本
- 完整的中文界面
- 所有菜单、按钮和消息的中文翻译
- 针对中文用户优化的用户体验
- 保持所有原有功能

### v1.0.0 - 初始版本
- 美观的悬浮球和渐变效果
- 右键上下文菜单功能
- 隐藏/显示功能
- 系统托盘集成
- 拖放定位
- 流畅动画和悬停效果
- 键盘快捷键 (Ctrl+H)
- 始终置顶行为
- 透明背景
- 无外部依赖

## 未来增强功能

可以添加的潜在功能：
- 多种球体主题
- 透明度控制
- 自动隐藏功能
- 全局热键
- 球体物理/弹跳效果
- 多个悬浮对象
- 更多自定义选项
