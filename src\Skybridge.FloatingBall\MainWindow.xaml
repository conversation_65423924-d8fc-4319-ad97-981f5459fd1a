<Window
    x:Class="Skybridge.FloatingBall.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Skybridge.FloatingBall"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="悬浮球"
    Width="80"
    Height="80"
    AllowsTransparency="True"
    Background="Transparent"
    MouseLeftButtonDown="Window_MouseLeftButtonDown"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    ToolTip="双击可以打开网页"
    Topmost="True"
    WindowStyle="None"
    mc:Ignorable="d">

    <Window.Resources>
        <!--  Gradient brushes for the ball  -->
        <RadialGradientBrush x:Key="BallGradient">
            <GradientStop Offset="0" Color="#FF3389F1" />
            <GradientStop Offset="0.7" Color="#FF2470D1" />
            <GradientStop Offset="1" Color="#FF1557B1" />
        </RadialGradientBrush>

        <RadialGradientBrush x:Key="BallHoverGradient">
            <GradientStop Offset="0" Color="#FF4A9AF5" />
            <GradientStop Offset="0.7" Color="#FF3580E1" />
            <GradientStop Offset="1" Color="#FF2667C1" />
        </RadialGradientBrush>

        <!--  Drop shadow effect  -->
        <DropShadowEffect
            x:Key="BallShadow"
            BlurRadius="8"
            Direction="315"
            Opacity="0.3"
            ShadowDepth="3"
            Color="Black" />

        <!--  Animation storyboards  -->
        <Storyboard x:Key="HoverAnimation">
            <DoubleAnimation
                Storyboard.TargetName="FloatingBall"
                Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                To="1.1"
                Duration="0:0:0.2" />
            <DoubleAnimation
                Storyboard.TargetName="FloatingBall"
                Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                To="1.1"
                Duration="0:0:0.2" />
        </Storyboard>

        <Storyboard x:Key="UnhoverAnimation">
            <DoubleAnimation
                Storyboard.TargetName="FloatingBall"
                Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                To="1.0"
                Duration="0:0:0.2" />
            <DoubleAnimation
                Storyboard.TargetName="FloatingBall"
                Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                To="1.0"
                Duration="0:0:0.2" />
        </Storyboard>

        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation
                AutoReverse="True"
                Storyboard.TargetName="InnerGlow"
                Storyboard.TargetProperty="Opacity"
                From="0.3"
                To="0.8"
                Duration="0:0:2" />
        </Storyboard>

        <!--  Modern Context Menu Style  -->
        <Style x:Key="ModernContextMenu" TargetType="ContextMenu">
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#E0E0E0" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Padding" Value="8" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ContextMenu">
                        <Border
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Effect="{StaticResource BallShadow}">
                            <ItemsPresenter />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Modern MenuItem Style  -->
        <Style x:Key="ModernMenuItem" TargetType="MenuItem">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="2" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="MenuItem">
                        <Border
                            x:Name="Border"
                            Padding="{TemplateBinding Padding}"
                            Background="Transparent"
                            CornerRadius="6">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter
                                    Grid.Column="0"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    ContentSource="Icon" />
                                <ContentPresenter
                                    Grid.Column="1"
                                    VerticalAlignment="Center"
                                    ContentSource="Header" />
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#F0F8FF" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#E3F2FD" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Modern Separator Style  -->
        <Style x:Key="ModernSeparator" TargetType="Separator">
            <Setter Property="Background" Value="#E0E0E0" />
            <Setter Property="Height" Value="1" />
            <Setter Property="Margin" Value="8,4" />
        </Style>


        <!--  Exit Icon (X)  -->
        <Canvas
            x:Key="ExitIcon"
            Width="16"
            Height="16">
            <Line
                Stroke="#DC3545"
                StrokeThickness="2"
                X1="4"
                X2="12"
                Y1="4"
                Y2="12" />
            <Line
                Stroke="#DC3545"
                StrokeThickness="2"
                X1="12"
                X2="4"
                Y1="4"
                Y2="12" />
        </Canvas>
        <DrawingImage x:Key="SettingImage">
            <DrawingImage.Drawing>
                <DrawingGroup ClipGeometry="M0,0 V1024 H1025 V0 H0 Z">
                    <GeometryDrawing Brush="#FF3289F1" Geometry="F1 M1025,1024z M0,0z M616.59648,33.934865C616.59648,15.193156,601.403321,0,582.66165,0L441.340822,0C422.59915,0,407.405992,15.193156,407.405992,33.934865L407.405992,136.612771 435.230543,103.232556C363.206268,116.416801,296.479426,149.052592,241.29995,197.306995L284.994913,196.479587 207.711691,133.896905C193.146703,122.102366,171.777942,124.348303,159.983404,138.913346L71.047415,248.740241C59.252877,263.305285,61.498792,284.673947,76.063779,296.468487L154.771068,360.204443 146.094963,318.031694C114.150753,378.748442 97.192077,446.759115 97.192077,517.194692 97.192077,523.159402 97.313721,529.110417 97.55595,535.045141L131.462709,533.660298 123.828966,500.595182 26.309914,523.109217C8.04864,527.325184,-3.337534,545.546657,0.878521,563.808016L32.668778,701.506782C36.884657,719.768142,55.106207,731.154181,73.367658,726.938214L170.001655,704.628494 131.136441,684.836497C160.367228,753.615764,207.214698,813.251457,266.430499,857.386775L256.209567,815.301952 211.737777,906.482453C203.521942,923.327389,210.517274,943.643148,227.362251,951.858972L354.380447,1013.809938C371.225423,1022.025762,391.54123,1015.030486,399.757065,998.18555L444.452193,906.547064 413.951647,891.670999 405.305203,924.485856C438.58997,933.256053 473.012789,937.737419 507.982566,937.737419 545.680772,937.737419 582.736684,932.52929 618.405429,922.361644L609.102566,889.726808 578.602019,904.602875 624.245407,998.18555C632.461241,1015.030486,652.777048,1022.025762,669.622025,1013.809938L796.640221,951.858972C813.485197,943.643148,820.48053,923.327389,812.264695,906.482453L765.618141,810.842998 755.922097,852.528591C812.677473,808.485893,857.494422,750.016074,885.604458,682.998449L846.677274,702.937706 950.634814,726.938214C968.896265,731.154181,987.117815,719.768142,991.333694,701.506782L1023.12395,563.808016C1027.340006,545.546657,1015.953832,527.325184,997.692557,523.109217L892.206963,498.755949 884.573219,531.821067 918.485804,533.050629C918.67701,527.777291 918.772877,522.491373 918.772877,517.194692 918.772877,448.699664 902.737214,382.485253 872.451178,323.017813L863.567978,364.790556 947.938692,296.468487C962.50368,284.673947,964.749594,263.305285,952.955057,248.740241L864.019068,138.913346C852.22453,124.348303,830.855768,122.102366,816.290781,133.896905L734.807393,199.880932 778.843277,201.010582C725.148601,152.767205,660.048684,119.487657,589.515917,104.941543L616.59648,138.176991 616.59648,33.934865z M575.807382,171.412439C634.44533,183.505427 688.652359,211.216511 733.483255,251.49597 745.889721,262.642935 764.557418,263.121814 777.519139,252.625618L859.002527,186.641592 811.274417,181.62515 900.210406,291.452045 905.226946,243.723801 820.856055,312.04587C808.406157,322.12772 804.702632,339.543121 811.973032,353.818613 837.42208,403.788518 850.90304,459.454632 850.90304,517.194692 850.90304,521.671408 850.822179,526.137482 850.660634,530.591504 850.071481,546.843016 861.094135,561.227986 876.939652,564.886182L982.425247,589.23945 956.993677,548.540649 925.203421,686.239414 965.902301,660.807982 861.944585,636.807472C845.84501,633.090581 829.408397,641.509685 823.017401,656.746729 799.42709,712.988642 761.835873,762.031549 714.313269,798.909539 701.621848,808.758164 697.575106,826.156496 704.617225,840.595129L751.263779,936.234584 766.888077,890.858063 639.869881,952.809029 685.246323,968.433419 639.602935,874.850744C632.392563,860.067023 615.61803,852.582848 599.799702,857.091972 570.165319,865.539526 539.370284,869.867689 507.982566,869.867689 478.864066,869.867689 450.2528,866.142909 422.598091,858.856141 406.940425,854.730522 390.549363,862.241675 383.451277,876.794933L338.756149,968.433419 384.13259,952.809029 257.114394,890.858063 272.738692,936.234584 317.210483,845.054083C324.361357,830.392571 320.068679,812.717582 306.98955,802.969259 257.418946,766.022785 218.128596,716.007136 193.599206,658.290259 187.160188,643.139042 170.774952,634.794948 154.734168,638.498262L58.100171,660.807982 98.799051,686.239414 67.008794,548.540649 41.577225,589.23945 139.096276,566.725415C154.998643,563.054055 166.035244,548.582564 165.369291,532.275456 165.164491,527.263405 165.061914,522.23581 165.061914,517.194692 165.061914,457.821835 179.317937,400.649887 206.159095,349.632508 213.696971,335.305064 210.064419,317.648068 197.48299,307.459759L118.775526,243.723801 123.792066,291.452045 212.728055,181.62515 164.999945,186.641592 242.28299,249.224273C255.110886,259.612033 273.552596,259.262821 285.977953,248.396865 332.03659,208.118685 387.580116,180.952548 447.451101,169.992986 463.568331,167.042695 475.275652,152.997731 475.275652,136.612771L475.275652,33.934865 441.340822,67.869729 582.66165,67.869729 548.726819,33.934865 548.726819,138.176991C548.726819,154.276789,560.039548,168.160585,575.807382,171.412439z M730.345048,517.194692C730.345048,391.885469 630.986681,289.955896 507.982566,289.955896 384.978273,289.955896 285.619906,391.885469 285.619906,517.194692 285.619906,642.503915 384.978273,744.433487 507.982566,744.433487 630.986681,744.433487 730.345048,642.503915 730.345048,517.194692z M353.489567,517.194692C353.489567,428.986289 422.85515,357.825626 507.982566,357.825626 593.109804,357.825626 662.475388,428.986289 662.475388,517.194692 662.475388,605.403095 593.109804,676.563758 507.982566,676.563758 422.85515,676.563758 353.489567,605.403095 353.489567,517.194692z" />
                </DrawingGroup>
            </DrawingImage.Drawing>
        </DrawingImage>

        <DrawingImage x:Key="AboutImage">
            <DrawingImage.Drawing>
                <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                    <GeometryDrawing Brush="#FF3289F1" Geometry="F1 M1024,1024z M0,0z M512,64C759.424,64 960,264.576 960,512 960,759.424 759.424,960 512,960 264.576,960 64,759.424 64,512 64,264.576 264.576,64 512,64z M512,149.333333C311.701333,149.333333 149.333333,311.701333 149.333333,512 149.333333,712.298667 311.701333,874.666667 512,874.666667 712.298667,874.666667 874.666667,712.298667 874.666667,512 874.666667,311.701333 712.298667,149.333333 512,149.333333z M533.333333,426.666667A21.333333,21.333333,0,0,1,554.666667,448L554.666667,746.666667A21.333333,21.333333,0,0,1,533.333333,768L490.666667,768A21.333333,21.333333,0,0,1,469.333333,746.666667L469.333333,448A21.333333,21.333333,0,0,1,490.666667,426.666667L533.333333,426.666667z M533.333333,256A21.333333,21.333333,0,0,1,554.666667,277.333333L554.666667,320A21.333333,21.333333,0,0,1,533.333333,341.333333L490.666667,341.333333A21.333333,21.333333,0,0,1,469.333333,320L469.333333,277.333333A21.333333,21.333333,0,0,1,490.666667,256L533.333333,256z" />
                </DrawingGroup>
            </DrawingImage.Drawing>
        </DrawingImage>

        <DrawingImage x:Key="HideImage">
            <DrawingImage.Drawing>
                <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                    <GeometryDrawing Brush="#FF3289F1" Geometry="F1 M1024,1024z M0,0z M253.6,679.2L363.2,569.6C356,552 352,532.8 352,512 352,424 424,352 512,352 532.8,352 552,356 569.6,363.2L652,280.8C607.2,264.8 560,256 512,256 344,256 182.4,362.4 128,512 152,577.6 196.8,635.2 253.6,679.2z" />
                    <GeometryDrawing Brush="#FF3289F1" Geometry="F1 M1024,1024z M0,0z M416,512L416,516.8 516.8,416 512,416C459.2,416,416,459.2,416,512z M770.4,344.8L933.6,181.6 888,136 134.4,889.6 180,935.2 372.8,742.4A390.4,390.4,0,0,0,512,768C679.2,768 842.4,661.6 896.8,512 872.8,446.4 827.2,388.8 770.4,344.8z M512,672C492,672,472,668,454.4,660.8L508,607.2 512.8,607.2C565.6,607.2,608.8,564,608.8,511.2L608.8,506.4 662.4,452.8C668,472 672,492 672,512 672,600 600,672 512,672z" />
                </DrawingGroup>
            </DrawingImage.Drawing>
        </DrawingImage>

        <DrawingImage x:Key="ShowImage">
            <DrawingImage.Drawing>
                <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                    <GeometryDrawing Brush="#FF3289F1" Geometry="F1 M1024,1024z M0,0z M512,256C344,256 182.4,362.4 128,512 182.4,661.6 344,768 512,768 679.2,768 842.4,661.6 896.8,512 841.6,362.4 679.2,256 512,256z M512,672C424,672 352,600 352,512 352,424 424,352 512,352 600,352 672,424 672,512 672,600 600,672 512,672z M608,512C608,564.8 564.8,608 512,608 459.2,608 416,564.8 416,512 416,459.2 459.2,416 512,416 564.8,416 608,459.2 608,512z" />
                </DrawingGroup>
            </DrawingImage.Drawing>
        </DrawingImage>

        <!--  URL Icon (Globe)  -->
        <DrawingImage x:Key="UrlImage">
            <DrawingImage.Drawing>
                <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                    <GeometryDrawing Brush="#FF3289F1" Geometry="F1 M1024,1024z M0,0z M765.5,70.3C685.2,23.9 597.5,1.9 511,1.9 334.8,1.9 163.4,93.3 68.9,256.9 -71.9,500.8 11.6,812.7 255.5,953.5 335.8,999.9 423.5,1021.9 510,1021.9 574.2,1021.9 637.8,1009.6 697.2,986.3L707.6,982.7C707.5,982.5 707.5,982.4 707.4,982.2 806.8,940.6 894,867.5 952.1,766.9 1092.9,523 1009.3,211.1 765.5,70.2999999999998z M967.8,438.3C917.9,440.1 861.2,431.2 802.5,412.8 830.6,321.4 839.6,238.2 828.3,175.1 903.7,246.2 951.8,339.2 967.8,438.3z M549.1,768.2C510.5,734.8 466.9,703.6 419.3,676.2 369.5,647.4 318.4,624.5 268,607.6 286,553.2 310.7,497 341.4,441.2L656.2,623C625.7,673.1,588.8,723.4,549.1,768.2z M278.7,913.3C243.6,893.1 227.8,831.3 236.3,748 239.4,717.6 245.8,685.4 254.6,652.3 301.8,668.2 349.8,689.5 396.1,716.2 440.2,741.7 481,770.9 517.2,802.1 448.1,872.1 374.2,921.6 313.3,921.6 299.9,921.8 288.6,919 278.7,913.3z M364.6,400.9C393.4,353.7 427.8,306.4 464.8,263.7 505.2,302.7 552.1,339.3 604,371.5 650,400.1 697.3,423.4 744.1,441.6 727.1,488.1 705.3,535.6 679.4,582.7L364.6,400.9z M628.5,332.1C579.4,301.6 534.6,266.5 496.2,229.1 567.4,155.1 644.6,102 707.6,102 721.1,102 732.4,104.8 742.3,110.4 777.4,130.6 793.2,192.4 784.7,275.7 780.8,314 772,355 759.1,397.4 715.2,380.4 671.1,358.6 628.5,332.1z M643.7,68.1C587.3,88.3 524.7,132.5 463.6,195.2 426.2,153.3 397.3,109.7 379.6,67.6 421.8,55.1 465.9,48.3 511,48.3 556,48.3 600.7,55.2 643.7,68.1z M335.7,82.9C355.9,132.2 388.9,182.1 431.9,229.6 394.3,272.6 357.9,322.6 324.6,377.8L121.3,260.4C173.4,179.9,248.7,118.4,335.7,82.9z M98.4,300.8L301.4,418C268.5,477.7 242.6,537.3 223.8,594.1 163,577.9 104.3,571 51.6,574.6 38.6,480.6 54.7,385.9 98.4,300.8z M62.7,631.9C61.7,628.2 61.2,624.5 60.3,620.8 105.8,618.6 157,625.1 210.3,639.2 188.7,719.5 182.4,792.3 192.6,848.9 130.6,790.5 85.4,716.3 62.7,631.9z M377.3,955.7C431.9,936.2 492.5,893.9 551.8,834.1 591.3,873.2 623.2,914.4 644.8,955.2 601.6,968.4 556.3,975.6 510,975.6 465,975.5 420.3,968.6 377.3,955.7z M689,939.5C665,892 629,844.6 583.9,800.1 623.4,755.7 661.6,703.8 696.5,646L899.7,763.3C848.3,842.7,774.4,903.7,689,939.5z M922.5,723L719.6,605.8C747.3,555.6 770.1,505.4 787.8,456.9 845.9,475.3 902.5,485.1 954.1,485.1 960.5,485.1 966.7,484.7 972.9,484.4 977.8,564.7 961.5,647 922.5,723z" />
                </DrawingGroup>
            </DrawingImage.Drawing>
        </DrawingImage>

        <!--  Star Icon for Center  -->

        <DrawingImage x:Key="StarIcon">
            <DrawingImage.Drawing>
                <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                    <GeometryDrawing Brush="#FFFFFFFF" Geometry="F1 M1024,1024z M0,0z M511.500488,63.937561C544.605159,63.937561 571.441951,90.774353 571.441951,123.879024 571.441951,145.230173 560.278852,163.973869 543.470267,174.5925L543.469268,255.750244 831.188293,255.750244C848.844051,255.750244,863.157073,270.063266,863.157073,287.719024L863.157073,863.157073C863.157073,880.812831,848.844051,895.125853,831.188293,895.125854L191.812683,895.125854C174.156925,895.125854,159.843903,880.812832,159.843902,863.157073L159.843902,287.719024C159.843902,270.063266,174.156924,255.750244,191.812683,255.750244L479.531707,255.750244 479.531707,174.5925C462.982868,164.139708,451.906685,145.810607,451.567017,124.871056L451.559024,123.879024C451.559024,90.774353,478.395816,63.937561,511.500488,63.937561z M791.227317,327.68L231.773659,327.68 231.773659,823.196098 791.227317,823.196098 791.227317,327.68z M631.383415,672.343415A7.992195,7.992195,0,0,1,639.37561,680.33561L639.37561,728.28878A7.992195,7.992195,0,0,1,631.383415,736.280976L391.617561,736.280976A7.992195,7.992195,0,0,1,383.625366,728.28878L383.625366,680.33561A7.992195,7.992195,0,0,1,391.617561,672.343415L631.383415,672.343415z M119.882927,399.609756A7.992195,7.992195,0,0,1,127.875122,407.601951L127.875122,711.305366A7.992195,7.992195,0,0,1,119.882927,719.297561L71.929756,719.297561A7.992195,7.992195,0,0,1,63.937561,711.305366L63.937561,407.601951A7.992195,7.992195,0,0,1,71.929756,399.609756L119.882927,399.609756z M951.07122,399.609756A7.992195,7.992195,0,0,1,959.063415,407.601951L959.063415,711.305366A7.992195,7.992195,0,0,1,951.07122,719.297561L903.118049,719.297561A7.992195,7.992195,0,0,1,895.125854,711.305366L895.125854,407.601951A7.992195,7.992195,0,0,1,903.118049,399.609756L951.07122,399.609756z M343.66439,463.547317C374.562216,463.547317 399.609756,488.594857 399.609756,519.492683 399.609756,550.390509 374.562216,575.438049 343.66439,575.438049 312.766564,575.438049 287.719024,550.390509 287.719024,519.492683 287.719024,488.594857 312.766564,463.547317 343.66439,463.547317z M679.336585,463.547317C710.234411,463.547317 735.281951,488.594857 735.281951,519.492683 735.281951,550.390509 710.234411,575.438049 679.336585,575.438049 648.438759,575.438049 623.391219,550.390509 623.39122,519.492683 623.391221,488.594857 648.43876,463.547317 679.336585,463.547317z" />
                </DrawingGroup>
            </DrawingImage.Drawing>
        </DrawingImage>
    </Window.Resources>

    <Grid>
        <!--  Context Menu  -->
        <Grid.ContextMenu>
            <ContextMenu x:Name="MainContextMenu" Style="{StaticResource ModernContextMenu}">
                <!--  URL Section (will be populated dynamically)  -->
                <MenuItem
                    x:Name="UrlSectionHeader"
                    FontWeight="Bold"
                    Foreground="#3389F1"
                    Header="快捷链接"
                    IsEnabled="False" />
                <Separator
                    x:Name="UrlSeparator"
                    Style="{StaticResource ModernSeparator}"
                    Visibility="Collapsed" />

                <!--  Control Section  -->
                <MenuItem
                    Click="ShowMenuItem_Click"
                    Header="显示"
                    Style="{StaticResource ModernMenuItem}">
                    <MenuItem.Icon>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <Image
                                    Width="16"
                                    Height="16"
                                    Source="{StaticResource ShowImage}" />
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem
                    Click="HideMenuItem_Click"
                    Header="隐藏"
                    Style="{StaticResource ModernMenuItem}">
                    <MenuItem.Icon>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <Image
                                    Width="16"
                                    Height="16"
                                    Source="{StaticResource HideImage}" />
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator Style="{StaticResource ModernSeparator}" />

                <!--  Settings Section  -->
                <MenuItem
                    Click="SettingsMenuItem_Click"
                    Header="设置"
                    Style="{StaticResource ModernMenuItem}"
                    Visibility="Collapsed">
                    <MenuItem.Icon>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <Image
                                    Width="14"
                                    Height="14"
                                    Source="{StaticResource SettingImage}" />
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem
                    Click="AboutMenuItem_Click"
                    Header="关于"
                    Style="{StaticResource ModernMenuItem}"
                    Visibility="Collapsed">
                    <MenuItem.Icon>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <Image
                                    Width="14"
                                    Height="14"
                                    Source="{StaticResource AboutImage}" />
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator Style="{StaticResource ModernSeparator}" Visibility="Collapsed" />

                <!--  Exit Section  -->
                <MenuItem
                    Click="ExitMenuItem_Click"
                    Header="退出"
                    Style="{StaticResource ModernMenuItem}">
                    <MenuItem.Icon>
                        <ContentPresenter Content="{StaticResource ExitIcon}" />
                    </MenuItem.Icon>
                </MenuItem>
            </ContextMenu>
        </Grid.ContextMenu>

        <!--  The floating ball  -->
        <Ellipse
            x:Name="FloatingBall"
            Width="60"
            Height="60"
            Cursor="Hand"
            Effect="{StaticResource BallShadow}"
            Fill="{StaticResource BallGradient}"
            MouseEnter="FloatingBall_MouseEnter"
            MouseLeave="FloatingBall_MouseLeave">
            <Ellipse.RenderTransform>
                <ScaleTransform CenterX="30" CenterY="30" ScaleX="1" ScaleY="1" />
            </Ellipse.RenderTransform>
        </Ellipse>

        <!--  Inner glow effect  -->
        <Ellipse
            x:Name="InnerGlow"
            Width="40"
            Height="40"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            IsHitTestVisible="False"
            Opacity="0.5">
            <Ellipse.Fill>
                <RadialGradientBrush>
                    <GradientStop Offset="0" Color="#AAFFFFFF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
            </Ellipse.Fill>
        </Ellipse>

        <!--  Center icon  -->
        <ContentPresenter
            x:Name="CenterIcon"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            IsHitTestVisible="False">
            <ContentPresenter.Content>
                <Image
                    Width="30"
                    Height="30"
                    Source="{StaticResource StarIcon}" />
            </ContentPresenter.Content>
        </ContentPresenter>
    </Grid>
</Window>