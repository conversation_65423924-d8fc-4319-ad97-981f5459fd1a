﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.FloatingBall
{
    public class Program
    {
        public const string AppName = "QuickBotFloatingBall";
        private const string MutexName = "Global\\QuickBotFloatingBall"; // 确保这个名称是唯一的
        public static readonly string PipeName = $"{Environment.MachineName}-{Environment.UserName}-{AppName}";

        private static App _app;

        public static Action<string[]> ArgumentReceived;

        [STAThread]
        public static void Main(string[] args)
        {
            Environment.CurrentDirectory = AppDomain.CurrentDomain.BaseDirectory;

            using (SingleInstanceManager singleInstanceManager = new SingleInstanceManager(MutexName, PipeName, true, args))
            {
                if (!singleInstanceManager.IsSingleInstance || singleInstanceManager.IsFirstInstance)
                {
                    singleInstanceManager.ArgumentsReceived += SingleInstanceManager_ArgumentsReceived;
                    _app = new App();
                    _app.Run();
                }
            }
        }

        private static void SingleInstanceManager_ArgumentsReceived(string[] arguments)
        {
            string message = "Arguments received: ";

            if (arguments == null)
            {
                message += "null";
            }
            else
            {
                message += "\"" + string.Join(" ", arguments) + "\"";
            }
            Console.WriteLine(message);
            ArgumentReceived?.Invoke(arguments);
        }
    }
}