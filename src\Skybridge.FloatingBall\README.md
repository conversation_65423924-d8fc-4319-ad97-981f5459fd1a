# Floating Ball Application

A beautiful WPF floating ball application with context menu functionality and system tray integration.

## Features

### 🎨 Beautiful Design
- **Gradient Ball**: Beautiful gradient with theme color #3389F1 and radial lighting effects
- **Smooth Animations**: Hover effects with scaling animations
- **Pulsing Glow**: Continuous subtle pulsing animation for visual appeal
- **Drop Shadow**: Realistic shadow effect for depth

### 🖱️ Interactive Features
- **Draggable**: Click and drag the ball to move it around the screen
- **Hover Effects**: Ball changes appearance and scales up when hovered
- **Right-Click Menu**: Context menu with various options

### 📋 Context Menu Options
- **Quick Links**: Dynamic section showing your saved URLs (click to open in browser)
- **Show**: Make the ball visible (when hidden)
- **Hide**: Hide the ball from view
- **Settings**: Modern settings window for URL management
- **About**: Information about the application
- **Exit**: Close the application

### 🔧 System Integration
- **System Tray**: Minimizes to system tray with custom themed icon
- **Tray Context Menu**: Right-click tray icon for quick actions
- **Double-Click Tray**: Double-click tray icon to toggle visibility
- **Always on Top**: Ball stays above other windows
- **No Taskbar**: Doesn't clutter the taskbar
- **Position Memory**: Remembers last position when reopened
- **Smart Positioning**: Initially appears in bottom-right corner

### 🔗 URL Management
- **Quick Access**: Add frequently used URLs for one-click access
- **Settings Window**: Modern interface for managing URLs
- **URL Validation**: Automatic validation of URL format
- **Persistent Storage**: URLs are saved and restored between sessions
- **Browser Integration**: Opens URLs in your default browser

### ⌨️ Keyboard Shortcuts
- **Ctrl+H**: Toggle ball visibility (when ball window is focused)

## Technical Details

### Requirements
- .NET Framework 4.6.1 or higher
- Windows operating system
- No additional NuGet packages required

### Architecture
- **WPF Application**: Built using Windows Presentation Foundation
- **XAML Styling**: Beautiful UI defined in XAML with gradients and animations
- **System.Windows.Forms Integration**: For system tray functionality
- **Event-Driven**: Responsive to mouse and keyboard events

### Window Properties
- **Transparent Background**: Seamless integration with desktop
- **No Window Chrome**: Borderless window for clean appearance
- **Topmost**: Always stays above other windows
- **No Resize**: Fixed size for consistent appearance

## Usage

### Basic Operations
1. **Launch**: Run the executable to start the floating ball (appears in bottom-right corner)
2. **Move**: Click and drag to reposition the ball (position is automatically saved)
3. **Hide/Show**: Right-click and select Hide/Show, or use Ctrl+H
4. **System Tray**: Access from system tray when hidden
5. **Exit**: Right-click and select Exit, or exit from system tray

### URL Management
1. **Open Settings**: Right-click the ball and select "Settings"
2. **Add URLs**: Enter a name and URL, then click "Add"
3. **Edit URLs**: Select a URL from the list, modify, and click "Update"
4. **Delete URLs**: Select a URL and click "Delete"
5. **Access URLs**: Right-click the ball and click any URL name to open it in your browser

### Features
- **Position Memory**: The ball remembers its last position when you restart
- **Modern UI**: Beautiful, modern interface with smooth animations
- **URL Validation**: Ensures URLs are properly formatted before saving
- **Persistent Storage**: All settings and URLs are saved automatically

## Building

Build using Visual Studio or MSBuild:

```bash
MSBuild.exe Skybridge.FloatingBall.csproj /p:Configuration=Release
```

## Customization

The application can be easily customized by modifying:

- **Colors**: Change gradient colors in MainWindow.xaml resources
- **Size**: Modify Width/Height properties of the ball
- **Animations**: Adjust animation durations and effects
- **Position**: Change initial positioning logic in InitializeFloatingBall()
- **Icon**: Replace the system tray icon creation logic

## Future Enhancements

Potential features that could be added:
- Settings dialog for customization
- Multiple ball themes
- Opacity controls
- Auto-hide functionality
- Global hotkeys
- Ball physics/bouncing effects
- Multiple floating objects
