﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace Skybridge.FloatingBall
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            var arguments = e.Args;
            if (arguments.Length == 2)
            {
                SetUrlValue(arguments[0], arguments[1]);
            }
            MainWindow mainWindow = new MainWindow();
            mainWindow.ShowDialog();
        }

        private void SetUrlValue(string name, string value)
        {
            if (FloatingBall.Properties.Settings.Default.UrlList == null)
            {
                FloatingBall.Properties.Settings.Default.UrlList = new StringCollection();
            }

            FloatingBall.Properties.Settings.Default.UrlList.Clear();

            FloatingBall.Properties.Settings.Default.UrlList.Add($"{name}|{value}");

            FloatingBall.Properties.Settings.Default.Save();
        }
    }
}