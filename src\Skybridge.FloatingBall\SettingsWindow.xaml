<Window
    x:Class="Skybridge.FloatingBall.SettingsWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="悬浮球设置"
    Width="680"
    Height="650"
    Background="#F8F9FA"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.Resources>
        <!--  Modern button style  -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#3389F1" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="15,8" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A9AF5" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#2470D1" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Secondary button style  -->
        <Style
            x:Key="SecondaryButton"
            BasedOn="{StaticResource ModernButton}"
            TargetType="Button">
            <Setter Property="Background" Value="#6C757D" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#7C858D" />
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#5C656D" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!--  Danger button style  -->
        <Style
            x:Key="DangerButton"
            BasedOn="{StaticResource ModernButton}"
            TargetType="Button">
            <Setter Property="Background" Value="#DC3545" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E85565" />
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#CC2535" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!--  Modern TextBox style  -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#DEE2E6" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#3389F1" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Modern ListBox style  -->
        <Style x:Key="ModernListBox" TargetType="ListBox">
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#DEE2E6" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBox">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                            <ScrollViewer>
                                <ItemsPresenter />
                            </ScrollViewer>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Modern ListBoxItem style  -->
        <Style x:Key="ModernListBoxItem" TargetType="ListBoxItem">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="2" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4">
                            <ContentPresenter />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F8F9FA" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#E3F2FD" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- URL Icon for Settings Window -->
        <Canvas x:Key="UrlIconSettings" Width="16" Height="16">
            <Ellipse Canvas.Left="2" Canvas.Top="2" Width="12" Height="12"
                     Fill="Transparent" Stroke="#3389F1" StrokeThickness="1.5"/>
            <Line X1="8" Y1="2" X2="8" Y2="14" Stroke="#3389F1" StrokeThickness="1"/>
            <Line X1="2" Y1="8" X2="14" Y2="8" Stroke="#3389F1" StrokeThickness="1"/>
            <Path Data="M 5,4 Q 8,6 11,4" Stroke="#3389F1" StrokeThickness="1" Fill="Transparent"/>
            <Path Data="M 5,12 Q 8,10 11,12" Stroke="#3389F1" StrokeThickness="1" Fill="Transparent"/>
        </Canvas>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock
                Margin="0,0,0,5"
                FontSize="24"
                FontWeight="Bold"
                Foreground="#212529"
                Text="设置" />
            <TextBlock
                FontSize="14"
                Foreground="#6C757D"
                Text="管理您的网址和应用程序首选项" />
        </StackPanel>

        <!--  Main Content  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="200" />
            </Grid.ColumnDefinitions>

            <!--  URL List  -->
            <StackPanel Grid.Column="0">
                <TextBlock
                    Margin="0,0,0,10"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="#212529"
                    Text="快速访问网址" />

                <ListBox
                    x:Name="UrlListBox"
                    Height="350"
                    ItemContainerStyle="{StaticResource ModernListBoxItem}"
                    SelectionChanged="UrlListBox_SelectionChanged"
                    Style="{StaticResource ModernListBox}">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock
                                        FontSize="14"
                                        FontWeight="Medium"
                                        Text="{Binding Name}" />
                                    <TextBlock
                                        Margin="0,2,0,0"
                                        FontSize="12"
                                        Foreground="#6C757D"
                                        Text="{Binding Url}" />
                                </StackPanel>
                                <ContentPresenter
                                    Grid.Column="1"
                                    VerticalAlignment="Center"
                                    Content="{StaticResource UrlIconSettings}" />
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </StackPanel>

            <!--  URL Form  -->
            <StackPanel Grid.Column="2">
                <TextBlock
                    Margin="0,0,0,15"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="#212529"
                    Text="编辑网址" />

                <TextBlock
                    Margin="0,0,0,5"
                    FontSize="12"
                    FontWeight="Medium"
                    Foreground="#495057"
                    Text="名称:" />
                <TextBox
                    x:Name="UrlNameTextBox"
                    Margin="0,0,0,15"
                    Style="{StaticResource ModernTextBox}" />

                <TextBlock
                    Margin="0,0,0,5"
                    FontSize="12"
                    FontWeight="Medium"
                    Foreground="#495057"
                    Text="网址:" />
                <TextBox
                    x:Name="UrlTextBox"
                    Margin="0,0,0,20"
                    Style="{StaticResource ModernTextBox}" />

                <Button
                    x:Name="AddButton"
                    Margin="0,0,0,10"
                    Click="AddButton_Click"
                    Content="添加"
                    Style="{StaticResource ModernButton}" />

                <Button
                    x:Name="UpdateButton"
                    Margin="0,0,0,10"
                    Click="UpdateButton_Click"
                    Content="更新"
                    IsEnabled="False"
                    Style="{StaticResource ModernButton}" />

                <Button
                    x:Name="DeleteButton"
                    Margin="0,0,0,10"
                    Click="DeleteButton_Click"
                    Content="删除"
                    IsEnabled="False"
                    Style="{StaticResource DangerButton}" />

                <Button
                    x:Name="ClearButton"
                    Click="ClearButton_Click"
                    Content="清空"
                    Style="{StaticResource SecondaryButton}" />
            </StackPanel>
        </Grid>

        <!--  Footer  -->
        <StackPanel
            Grid.Row="2"
            Margin="0,30,0,0"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            <Button
                Margin="0,0,10,0"
                Click="SaveButton_Click"
                Content="保存并关闭"
                Style="{StaticResource ModernButton}" />
            <Button
                Click="CancelButton_Click"
                Content="取消"
                Style="{StaticResource SecondaryButton}" />
        </StackPanel>
    </Grid>
</Window>