﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Skybridge.FloatingBall
{
    public class MutexManager : IDisposable
    {
        public bool HasHandle { get; private set; }

        private Mutex mutex;

        public MutexManager(string mutexName) : this(mutexName, Timeout.Infinite)
        {
        }

        public MutexManager(string mutexName, int timeout)
        {
            mutex = new Mutex(false, mutexName);

            try
            {
                HasHandle = mutex.WaitOne(timeout, false);
            }
            catch (AbandonedMutexException)
            {
                HasHandle = true;
            }
        }

        public static bool IsRunning(string mutexName)
        {
            try
            {
                using (Mutex mutex = new Mutex(false, mutexName, out bool createdNew))
                {
                    return !createdNew;
                }
            }
            catch
            {
            }

            return false;
        }

        public void Dispose()
        {
            if (mutex != null)
            {
                if (HasHandle)
                {
                    mutex.ReleaseMutex();
                }

                mutex.Dispose();
            }
        }
    }
}