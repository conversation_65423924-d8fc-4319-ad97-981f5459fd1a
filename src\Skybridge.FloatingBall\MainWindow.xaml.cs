﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using System.Windows.Forms;

namespace Skybridge.FloatingBall
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _isHidden = false;
        private Storyboard _hoverStoryboard;
        private Storyboard _unhoverStoryboard;
        private Storyboard _pulseStoryboard;
        private NotifyIcon _notifyIcon;
        private bool _isCtrlPressed = false;

        public MainWindow()
        {
            InitializeComponent();
            InitializeFloatingBall();
            InitializeSystemTray();
            InitializeKeyboardHooks();
            InitializeUrlMenu();
            Program.ArgumentReceived += ArgumentReceived;
        }

        private void ArgumentReceived(string[] arguments)
        {
            if (arguments.Length == 2)
            {
                SetUrlValue(arguments[0], arguments[1]);
                System.Windows.Application.Current.Dispatcher.Invoke(RefreshUrlMenu);
            }
        }

        private void SetUrlValue(string name, string value)
        {
            if (Skybridge.FloatingBall.Properties.Settings.Default.UrlList == null)
            {
                Skybridge.FloatingBall.Properties.Settings.Default.UrlList = new StringCollection();
            }

            Skybridge.FloatingBall.Properties.Settings.Default.UrlList.Clear();

            Skybridge.FloatingBall.Properties.Settings.Default.UrlList.Add($"{name}|{value}");

            Skybridge.FloatingBall.Properties.Settings.Default.Save();
        }

        private void InitializeFloatingBall()
        {
            // Restore saved position or use default bottom-right position
            var workingArea = SystemParameters.WorkArea;

            if (Properties.Settings.Default.WindowLeft >= 0 && Properties.Settings.Default.WindowTop >= 0)
            {
                // Use saved position if valid
                this.Left = Properties.Settings.Default.WindowLeft;
                this.Top = Properties.Settings.Default.WindowTop;

                // Ensure the window is still visible on screen (in case screen resolution changed)
                if (this.Left > workingArea.Right - this.Width)
                    this.Left = workingArea.Right - this.Width - 20;
                if (this.Top > workingArea.Bottom - this.Height)
                    this.Top = workingArea.Bottom - this.Height - 20;
                if (this.Left < workingArea.Left)
                    this.Left = workingArea.Left + 20;
                if (this.Top < workingArea.Top)
                    this.Top = workingArea.Top + 20;
            }
            else
            {
                // Default position: bottom-right corner of the screen
                this.Left = workingArea.Right - this.Width - 20;
                this.Top = workingArea.Bottom - this.Height - 20;
            }

            // Get storyboards from resources
            _hoverStoryboard = (Storyboard)this.Resources["HoverAnimation"];
            _unhoverStoryboard = (Storyboard)this.Resources["UnhoverAnimation"];
            _pulseStoryboard = (Storyboard)this.Resources["PulseAnimation"];

            // Start the pulse animation
            _pulseStoryboard.Begin();
        }

        private void InitializeSystemTray()
        {
            // Create system tray icon
            _notifyIcon = new NotifyIcon();
            _notifyIcon.Icon = CreateTrayIcon();
            _notifyIcon.Text = @"QuickBot悬浮球";
            _notifyIcon.Visible = true;

            // Create context menu for system tray
            var contextMenu = new ContextMenuStrip();

            var showItem = new ToolStripMenuItem("显示悬浮球");
            showItem.Click += (s, e) => ShowBall();
            contextMenu.Items.Add(showItem);

            var hideItem = new ToolStripMenuItem("隐藏悬浮球");
            hideItem.Click += (s, e) => HideBall();
            contextMenu.Items.Add(hideItem);

            var resetItem = new ToolStripMenuItem("重置位置");
            resetItem.Click += (s, e) => ResetBall();
            contextMenu.Items.Add(resetItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            var exitItem = new ToolStripMenuItem("退出");
            exitItem.Click += (s, e) => System.Windows.Application.Current.Shutdown();
            contextMenu.Items.Add(exitItem);

            _notifyIcon.ContextMenuStrip = contextMenu;

            // Double-click to show/hide
            _notifyIcon.DoubleClick += (s, e) =>
            {
                if (_isHidden)
                    ShowBall();
                else
                    HideBall();
            };
        }

        private void ResetBall()
        {
            var workingArea = SystemParameters.WorkArea;
            this.Left = workingArea.Right - this.Width - 20;
            this.Top = workingArea.Bottom - this.Height - 20;
        }

        private Icon CreateTrayIcon()
        {
            // Create a simple icon for the system tray
            var bitmap = new Bitmap(16, 16);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                graphics.Clear(System.Drawing.Color.Transparent);

                // Draw a circle with the new theme color #3389F1
                using (var brush = new SolidBrush(System.Drawing.Color.FromArgb(51, 137, 241)))
                {
                    graphics.FillEllipse(brush, 2, 2, 12, 12);
                }

                // Add a white highlight
                using (var brush = new SolidBrush(System.Drawing.Color.FromArgb(100, 255, 255, 255)))
                {
                    graphics.FillEllipse(brush, 4, 4, 6, 6);
                }
            }

            return System.Drawing.Icon.FromHandle(bitmap.GetHicon());
        }

        private void InitializeUrlMenu()
        {
            RefreshUrlMenu();
        }

        private void RefreshUrlMenu()
        {
            // Remove existing URL menu items
            var itemsToRemove = new List<System.Windows.Controls.MenuItem>();
            var urlSeparatorIndex = -1;

            for (int i = 0; i < MainContextMenu.Items.Count; i++)
            {
                var item = MainContextMenu.Items[i];
                if (item == UrlSeparator)
                {
                    urlSeparatorIndex = i;
                    break;
                }
            }

            // Remove old URL items (between header and separator)
            if (urlSeparatorIndex > 1)
            {
                for (int i = urlSeparatorIndex - 1; i > 0; i--)
                {
                    var item = MainContextMenu.Items[i];
                    if (item == UrlSectionHeader)
                        break;
                    if (item is System.Windows.Controls.MenuItem)
                    {
                        itemsToRemove.Add((System.Windows.Controls.MenuItem)item);
                    }
                }
            }

            foreach (var item in itemsToRemove)
            {
                MainContextMenu.Items.Remove(item);
            }

            // Add URL items from settings
            if (Properties.Settings.Default.UrlList != null && Properties.Settings.Default.UrlList.Count > 0)
            {
                UrlSeparator.Visibility = Visibility.Visible;

                int insertIndex = MainContextMenu.Items.IndexOf(UrlSeparator);

                foreach (string urlData in Properties.Settings.Default.UrlList)
                {
                    var parts = urlData.Split('|');
                    if (parts.Length == 2)
                    {
                        var urlMenuItem = new System.Windows.Controls.MenuItem
                        {
                            Header = parts[0],
                            Tag = parts[1],
                            Style = (Style)this.Resources["ModernMenuItem"],
                            ToolTip = parts[1]
                        };

                        // Add icon - Windows 7 compatible
                        var iconCanvas = new Canvas
                        {
                            Width = 16,
                            Height = 16
                        };

                        // Create globe icon using shapes
                        var circle = new Ellipse
                        {
                            Width = 12,
                            Height = 12,
                            Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(51, 137, 241)),
                            StrokeThickness = 1.5,
                            Fill = System.Windows.Media.Brushes.Transparent
                        };
                        Canvas.SetLeft(circle, 2);
                        Canvas.SetTop(circle, 2);

                        var verticalLine = new Line
                        {
                            X1 = 8,
                            Y1 = 2,
                            X2 = 8,
                            Y2 = 14,
                            Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(51, 137, 241)),
                            StrokeThickness = 1
                        };

                        var horizontalLine = new Line
                        {
                            X1 = 2,
                            Y1 = 8,
                            X2 = 14,
                            Y2 = 8,
                            Stroke = new SolidColorBrush(System.Windows.Media.Color.FromRgb(51, 137, 241)),
                            StrokeThickness = 1
                        };

                        iconCanvas.Children.Add(circle);
                        iconCanvas.Children.Add(verticalLine);
                        iconCanvas.Children.Add(horizontalLine);

                        urlMenuItem.Icon = iconCanvas;

                        // Add click handler
                        urlMenuItem.Click += UrlMenuItem_Click;

                        MainContextMenu.Items.Insert(insertIndex, urlMenuItem);
                        insertIndex++;
                    }
                }
            }
            else
            {
                UrlSeparator.Visibility = Visibility.Collapsed;
            }
        }

        private void UrlMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as System.Windows.Controls.MenuItem;
            if (menuItem?.Tag is string url)
            {
                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = url,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"无法打开网址: {ex.Message}",
                        "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void InitializeKeyboardHooks()
        {
            // Add keyboard event handlers for global shortcuts
            this.KeyDown += MainWindow_KeyDown;
            this.KeyUp += MainWindow_KeyUp;

            // Make the window focusable to receive keyboard events
            this.Focusable = true;
        }

        private void MainWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.LeftCtrl || e.Key == Key.RightCtrl)
            {
                _isCtrlPressed = true;
            }
            else if (_isCtrlPressed && e.Key == Key.H)
            {
                // Ctrl+H to toggle visibility
                if (_isHidden)
                    ShowBall();
                else
                    HideBall();
            }
        }

        private void MainWindow_KeyUp(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.LeftCtrl || e.Key == Key.RightCtrl)
            {
                _isCtrlPressed = false;
            }
        }

        #region Window Events

        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Allow dragging the window
            this.DragMove();
        }

        private void MainWindow_LocationChanged(object sender, EventArgs e)
        {
            // Save the current position when the window is moved
            //SaveWindowPosition();
        }

        #endregion Window Events

        #region Ball Events

        private void FloatingBall_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // Change the ball appearance on hover
            FloatingBall.Fill = (RadialGradientBrush)this.Resources["BallHoverGradient"];
            _hoverStoryboard.Begin();
        }

        private void FloatingBall_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // Restore the ball appearance
            FloatingBall.Fill = (RadialGradientBrush)this.Resources["BallGradient"];
            _unhoverStoryboard.Begin();
        }

        #endregion Ball Events

        #region Context Menu Events

        private void ShowMenuItem_Click(object sender, RoutedEventArgs e)
        {
            ShowBall();
        }

        private void HideMenuItem_Click(object sender, RoutedEventArgs e)
        {
            HideBall();
        }

        private void SettingsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.Owner = this;

            if (settingsWindow.ShowDialog() == true)
            {
                // Refresh the URL menu after settings are saved
                RefreshUrlMenu();
            }
        }

        private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.MessageBox.Show("QuickBot悬浮球 v1.0\n\nQuickBot悬浮球应用程序，带有右键菜单功能。\n\n功能特性:\n• 可拖拽的悬浮球\n• 右键上下文菜单\n• 隐藏/显示功能\n• 精美的动画效果\n• 快捷网址管理",
                          "关于悬浮球",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.Application.Current.Shutdown();
        }

        #endregion Context Menu Events

        #region Public Methods

        public void ShowBall()
        {
            if (_isHidden)
            {
                this.Visibility = Visibility.Visible;
                _isHidden = false;

                // Animate the ball appearing
                var fadeInAnimation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
                this.BeginAnimation(Window.OpacityProperty, fadeInAnimation);
            }
        }

        public void HideBall()
        {
            if (!_isHidden)
            {
                // Animate the ball disappearing
                var fadeOutAnimation = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(300));
                fadeOutAnimation.Completed += (s, e) =>
                {
                    this.Visibility = Visibility.Hidden;
                };
                this.BeginAnimation(Window.OpacityProperty, fadeOutAnimation);
                _isHidden = true;
            }
        }

        #endregion Public Methods

        #region Position Management

        private void SaveWindowPosition()
        {
            // Save current window position to settings
            Properties.Settings.Default.WindowLeft = this.Left;
            Properties.Settings.Default.WindowTop = this.Top;
            Properties.Settings.Default.Save();
        }

        #endregion Position Management

        #region Override Methods

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);

            // Make the window click-through when hidden (optional enhancement)
            // This could be useful for future features
        }

        protected override void OnClosed(EventArgs e)
        {
            // Save final position before closing
            SaveWindowPosition();

            // Clean up animations
            _pulseStoryboard?.Stop();
            _hoverStoryboard?.Stop();
            _unhoverStoryboard?.Stop();

            // Clean up system tray icon
            _notifyIcon?.Dispose();

            base.OnClosed(e);
        }

        #endregion Override Methods
    }
}