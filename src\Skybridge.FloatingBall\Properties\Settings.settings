﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="uri:settings" CurrentProfile="(Default)">
  <Profiles>
    <Profile Name="(Default)" />
  </Profiles>
  <Settings>
    <Setting Name="WindowLeft" Type="System.Double" Scope="User">
      <Value Profile="(Default)">-1</Value>
    </Setting>
    <Setting Name="WindowTop" Type="System.Double" Scope="User">
      <Value Profile="(Default)">-1</Value>
    </Setting>
    <Setting Name="UrlList" Type="System.Collections.Specialized.StringCollection" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>